import pandas as pd
from ExceldataCleaning import data_trim1,data_trim2
import pymysql
import re, os

def load_to_mysql_with_duplicate_checking(target_form, json_data, username, filenames, tablename,connect_info):
    """
    把json_data中的数据存到target_form中
    :param target_form: 要插入的数据表的名称
    :param json_data: 存储要插入数据的json文件
    :return: None
    """
    # 记录成功函数和失败行数
    suc_line = 0
    fail_line = 0


    # 整理json数据成格式合适的dataframe数据
    data = pd.DataFrame(json_data)
    if '交易日期' not in data.columns:
        data = data_trim1(data)
    else:
        data = data_trim2(data)
    data = data[['交易户名', '交易卡号', '交易账号', '交易时间', '收付标志', '交易金额', '交易余额', '交易币种', '对手账号', '对手户名', '交易网点名称', '对手开户银行', '备注', '摘要说明', 'IP地址', 'MAC地址']]
    data = data.astype(str)
    data = data.applymap(lambda x: None if pd.isna(x) or x == 'nan' else x)

    backup_connection = {
        "host":"localhost",
        "user":"root",
        "password":"root",
        "database":"digit_check"
    }

    connect_dic = connect_info if connect_info else backup_connection
    # 如果要插入的数据表不存在则创建
    connection = pymysql.connect(**connect_dic)
    cursor = connection.cursor()

    print("target",target_form)

    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {target_form} (
            `交易户名` VARCHAR(64) NOT NULL,
            `交易卡号` VARCHAR(64) NOT NULL,
            `交易账号` varchar(64),
            `交易时间` DATETIME,
            `收付标志` VARCHAR(64),
            `交易金额` DECIMAL(18, 2),
            `交易余额` DECIMAL(18,2),
            `交易币种` varchar(64),
            `对手账号` VARCHAR(64),
            `对手户名` VARCHAR(64),
            `交易网点名称` VARCHAR(64),
            `对手开户银行` varchar(64),
            `备注` varchar(512),
            `摘要说明` varchar(512),
            `IP地址` varchar(512),
            `MAC地址` varchar(512)
        );
        """
    print("即将执行的SQL语句如下：")
    print(create_table_query)

    cursor.execute(create_table_query)

    # ✅ 插入：检查并添加缺失字段，最开始数据表都没有IP地址和MAC地址字段，这段代码能补上
    required_columns = ['IP地址', 'MAC地址']
    cursor.execute(f"SHOW COLUMNS FROM {target_form}")
    existing_columns = set([col[0] for col in cursor.fetchall()])
    print()
    #print(existing_columns)
    for col in required_columns:
        if col not in existing_columns:
            cursor.execute(f"ALTER TABLE {target_form} ADD COLUMN `{col}` VARCHAR(512)")

    # 查原表中的所有人名，2.0新增
    cursor.execute(f"SELECT DISTINCT 交易户名, 对手户名 FROM {target_form}")
    org_members = set()
    for row in cursor.fetchall():
        org_members.update(row)  # 直接将两个元素分别加入 set

    # 查临时表中所有人名，2.0新增
    new_members = set(data["交易户名"].unique()) | set(data["对手户名"].unique())

    # 取交集，检测新数据中那些户名和原数据中的户名重复了，2.0新增
    intersection_list = list(org_members & new_members)
    if '' in intersection_list:
        intersection_list.remove('')

    # 把新数据插入到数据表中，并更新成功行数和失败行数
    insert_query = f"""
        INSERT INTO {target_form} (
            交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种,
            对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, `IP地址`, `MAC地址`
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

    for _, row in data.iterrows():

        if row['交易户名'] is None or row['交易卡号'] is None or row['交易时间'].strip() is None or row['交易金额'] == '':
            fail_line += 1
            continue
        suc_line += 1

        cursor.execute(insert_query, (
            row['交易户名'],
            row['交易卡号'],
            row['交易账号'],  # 新增字段
            row['交易时间'],
            row['收付标志'],
            row['交易金额'],
            row['交易余额'],  # 新增字段
            row['交易币种'],  # 新增字段
            row['对手账号'],
            row['对手户名'],
            row['交易网点名称'],
            row['对手开户银行'],  # 新增字段
            row['备注'],
            row['摘要说明'],  # 新增字段
            row['IP地址'],  # 后加的字段
            row['MAC地址']  # 后加的字段
        ))

    connection.commit()

    insert_query = f"""
        INSERT INTO log (user, filename,tablename) VALUES (%s,%s,%s);
        """
    for filename in filenames:
        cursor.execute(insert_query,(username, filename, tablename))
    connection.commit()

    # 关闭游标和连接, 2.0在result中加了intersection
    cursor.close()
    connection.close()
    result = {
        "success_lines": suc_line,
        "fail_lines": fail_line,
        "intersection": intersection_list
    }

    return result
