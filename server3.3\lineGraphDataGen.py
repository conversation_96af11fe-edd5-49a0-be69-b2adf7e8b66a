import pandas as pd


def aggregate_transactions_by_gap(gap, data):
    # 修复 2：强制创建副本
    data = data.copy()

    # 处理时间字段
    data['交易时间'] = pd.to_datetime(data['交易时间'])

    # 兼容中英文参数
    gap_mapping = {'year': '年', 'month': '月', '年': '年', '月': '月'}
    normalized_gap = gap_mapping.get(gap.lower() if isinstance(gap, str) else gap, gap)

    if normalized_gap not in ['年', '月']:
        raise ValueError("gap 只允许为 '年' 或 '月' (或英文 year/month)")

    # 生成时间段
    if normalized_gap == '月':
        data['时间段'] = data['交易时间'].dt.strftime('%Y-%m')
    else:
        data['时间段'] = data['交易时间'].dt.strftime('%Y')

    # 聚合逻辑
    aggregated = data.groupby('时间段', as_index=False)['交易金额'].sum()
    return {
        "timeSpan": aggregated['时间段'].tolist(),
        "tradeAmount": aggregated['交易金额'].tolist()
    }