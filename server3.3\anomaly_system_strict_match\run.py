# 使用绝对导入
import sys
import os

# 获取当前文件所在目录的父目录
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

# 将父目录添加到Python路径
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入模块
from anomaly_system_strict_match import data_loader
from anomaly_system_strict_match import feature_engineer
from anomaly_system_strict_match import fusion_model
from anomaly_system_strict_match.analyze_by_behavior import analyze_behavior_categories



def run_pipeline(excel_path: str = ""):
    print("▶ 加载数据中 ...")
    df = data_loader.load_excel(excel_path, drop_bad_time=True)

    print("▶ 提取行为特征 ...")
    df_feat = feature_engineer.extract_features(df)

    print("▶ 分析行为标签并计算命中标签数 ...")
    df_behav = analyze_behavior_categories(df_feat)

    print("▶ 运行多模型融合异常检测 ...")
    df_scores = fusion_model.detect_fused_anomalies(df_behav)

    df_all = df_behav.join(df_scores)

    #print("▶ 绘制 PCA 降维可视化图 ...")
    #visualizer.plot_pca(df_feat, df_scores)

    print("▶ 导出得分文件 ...")
    cols_to_export = [
        "ae_score", "fused_score", "anomaly_flag",
        "标签_快进快出", "标签_高频交易", "标签_时间集中", "标签_小额测试", "命中标签数"
        ,"is_partner_with_flagged"
    ]

    return df_all[cols_to_export] 
    df_all.to_excel("scores_fused.csv", encoding="utf-8", columns=cols_to_export, index=True)

    print("✅ 完成：已生成 scores_fused.csv")

    return 

    
