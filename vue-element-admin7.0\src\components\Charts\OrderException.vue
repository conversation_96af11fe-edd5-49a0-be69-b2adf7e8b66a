<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>异常物流订单分析系统</span>
      </div>

      <!-- 文件上传区域 -->
      <el-upload
        class="upload-container"
        action="#"
        :auto-upload="false"
        :limit="5"
        :on-change="handleFileChange"
        :file-list="localFiles"
        accept=".xlsx,.csv"
        :show-file-list="true"
      >
        <el-button type="primary" icon="el-icon-upload" @click="showServerFiles = true">选择本地文件（仅限.xlsx/.csv）</el-button>
        <div slot="tip" class="el-upload__tip">每次最多上传5个文件，单个文件不超过100MB</div>
      </el-upload>

      <!-- 服务器文件选择 -->
      <div v-if="showServerFiles" class="server-files-section">
        <el-table
          :data="serverFiles"
          border
          fit
          highlight-current-row
          @selection-change="handleSelectionChange"
          v-loading="loading"
          style="margin:20px 0;"
          height="200"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="fileName" label="服务器文件" min-width="250">
            <template #default="{row}">
              <i class="el-icon-folder-opened" />
              <span style="margin-left:8px">{{ row.fileName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="180" align="center" />
          <el-table-column prop="fileSize" label="文件大小" width="120" align="center">
            <template #default="{row}">
              {{ (row.fileSize / 1024 / 1024).toFixed(2) }} MB
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div v-if="showServerFiles" class="action-buttons">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          @click="loadServerFiles"
          :loading="loading"
        >
          刷新文件列表
        </el-button>
        <el-button
          type="success"
          icon="el-icon-s-data"
          :loading="processing"
          :disabled="selectedFiles.length === 0"
          @click="submitAnalysis"
        >
          {{ processing ? '分析中...' : '开始分析' }}
        </el-button>
      </div>

      <!-- 异常类型选择 -->
      <div v-if="Object.keys(anomalyData).length > 0" class="anomaly-type-selector">
        <el-divider content-position="left">异常数据类型</el-divider>
        <div class="scrollable-radio-group">
          <el-radio-group v-model="currentAnomalyType" @change="handleAnomalyTypeChange">
            <el-radio-button v-for="(value, key) in anomalyData" :key="key" :label="key">
              {{ anomalyTypeLabels[key] || key }} ({{ value.length }})
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 异常数据展示 -->
      <el-divider content-position="left">异常数据结果</el-divider>
      <div class="table-container">
        <el-table
          :data="exceptionData"
          border
          fit
          highlight-current-row
          v-loading="processing"
          style="width:100%;"
          height="500"
        >
          <el-table-column prop="订单号" label="订单号" min-width="140" align="center" />
          <el-table-column prop="支付人姓名" label="支付人姓名" min-width="100" />
          <el-table-column prop="支付人身份证号" label="身份证号" min-width="150" />
          <el-table-column prop="物流单号" label="物流单号" min-width="140" />
          <el-table-column prop="收货地址" label="收货地址" min-width="180" show-overflow-tooltip />
          <el-table-column prop="支付方式" label="支付方式" min-width="90" />
          <el-table-column prop="订单金额" label="订单金额" min-width="100" align="right" />
          <el-table-column prop="订单状态" label="订单状态" min-width="90" />
          <el-table-column prop="创建时间" label="创建时间" min-width="140" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'OrderException',
  data() {
    return {
      localFiles: [],
      serverFiles: [],
      selectedFiles: [],
      loading: false,
      processing: false,
      anomalyData: {},
      exceptionData: [],
      currentAnomalyType: '',
      showServerFiles: false,
      anomalyTypeLabels: {
        '同一姓名多个身份证': '同一姓名多个身份证',
        '同一身份证多个姓名': '同一身份证多个姓名',
        '物流单号重复': '物流单号重复',
        '订单号多个身份证': '订单号多个身份证'
      }
    }
  },
  mounted() {
    // 初始不加载服务器文件，等用户点击按钮后再加载
  },
  methods: {
    async loadServerFiles() {
      this.loading = true
      try {
        // 使用axios直接调用后端接口，避免可能的$http配置问题
        const response = await axios.get('http://127.0.0.1:8000/get_all_TrackingNum')
        const data = response.data
        // 根据后端返回的数据格式进行处理
        if (data.paths && Array.isArray(data.paths)) {
          this.serverFiles = data.paths.map(path => {
            const fileName = path.split('\\').pop()
            return {
              fileName: fileName,
              path: path,
              uploadTime: new Date().toLocaleString(), // 如果后端没有提供时间，使用当前时间
              fileSize: 1024 * 1024 // 默认大小，如果后端没有提供
            }
          })
          console.log('成功获取服务器文件列表:', this.serverFiles)
        } else {
          console.error('服务器返回的文件列表格式不正确:', data)
          this.$message.warning('服务器返回的文件列表格式不正确')
        }
      } catch (error) {
        console.error('获取服务器文件失败:', error)
        this.$message.error(`获取服务器文件失败: ${error.message || '未知错误'}`)
      } finally {
        this.loading = false
      }
    },
    handleFileChange(file, fileList) {
      if (fileList.length > 5) {
        this.$message.warning('最多选择5个文件')
        this.localFiles = fileList.slice(0, 5)
        return false
      }
      this.localFiles = fileList
      // 如果是第一次选择文件，自动加载服务器文件列表
      if (this.showServerFiles === false && fileList.length > 0) {
        this.showServerFiles = true
        this.loadServerFiles()
      }
      return false
    },
    handleSelectionChange(selection) {
      this.selectedFiles = selection.map(item => item.path || item.fileName)
      console.log('已选择文件:', this.selectedFiles)
    },

    handleAnomalyTypeChange(type) {
      this.exceptionData = this.anomalyData[type] || []
    },

    async submitAnalysis() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请至少选择一个文件进行分析')
        return
      }
      this.processing = true
      try {
        // 使用axios直接调用后端接口
        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {
          filenames: this.selectedFiles
        })
        const data = response.data
        console.log('分析结果:', data)
        // 清空之前的数据
        this.anomalyData = {}
        this.exceptionData = []
        this.currentAnomalyType = ''
        // 处理返回的异常数据
        if (data && typeof data === 'object') {
          this.anomalyData = data
          // 计算总异常记录数
          const totalAnomalies = Object.values(data).reduce((sum, arr) => sum + arr.length, 0)
          if (totalAnomalies > 0) {
            this.$message.success(`发现 ${totalAnomalies} 条异常记录`)
            // 默认显示第一个有数据的异常类型
            for (const key in data) {
              if (data[key].length > 0) {
                this.currentAnomalyType = key
                this.exceptionData = data[key]
                break
              }
            }
          } else {
            this.$message.info('未发现异常记录')
          }
        } else {
          this.$message.info('未发现异常记录')
        }
      } catch (error) {
        console.error('数据分析失败:', error)
        this.$message.error(`数据分析失败: ${error.message || '未知错误'}`)
      } finally {
        this.processing = false
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.upload-container {
  margin-bottom: 20px;
}

.server-files-section {
  margin: 20px 0;
}

.action-buttons {
  margin: 20px 0;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.anomaly-type-selector {
  margin: 20px 0;
}

.scrollable-radio-group {
  max-height: 120px;
  overflow-y: auto;
  padding: 10px 0;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  /* 强制显示滚动条，即使内容不够 */
  overflow-y: scroll;
}

.el-divider {
  margin: 24px 0;
}

.el-table {
  font-size: 14px;
}

.el-table::before {
  height: 0;
}

::v-deep .el-table th {
  background-color: #f5f7fa;
  color: #606266;
}

::v-deep .el-table__row:hover {
  background-color: #f5f7fa !important;
}

/* 减小表格行高 */
::v-deep .el-table__row {
  height: 40px; /* 默认可能是 48px 左右 */
}

/* 减小表头高度 */
::v-deep .el-table__header th {
  padding: 8px 0; /* 默认可能是 12px 0 */
}

/* 减小单元格内边距 */
::v-deep .el-table__cell {
  padding: 8px 0; /* 默认可能是 12px 0 */
}

/* 自定义滚动条样式 */
.scrollable-radio-group::-webkit-scrollbar {
  width: 6px;
}

.scrollable-radio-group::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}

.scrollable-radio-group::-webkit-scrollbar-track {
  background-color: #F2F6FC;
}

.table-container {
  width: 100%;
  height: 500px; /* 固定高度，超出时显示滚动条 */
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  margin-top: 20px;
}/* 自定义表格滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 8px;  /* 垂直滚动条宽度 */
}

.table-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-track {
  background-color: #F2F6FC;
}
</style>
