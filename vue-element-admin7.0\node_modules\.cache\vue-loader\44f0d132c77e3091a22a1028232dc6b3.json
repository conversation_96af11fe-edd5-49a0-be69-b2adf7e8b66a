{"remainingRequest": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749201152735}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1731739010000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>异常物流订单分析系统</span>\r\n      </div>\r\n\r\n      <!-- 文件上传区域 -->\r\n      <el-upload\r\n        class=\"upload-container\"\r\n        action=\"#\"\r\n        :auto-upload=\"false\"\r\n        :limit=\"5\"\r\n        :on-change=\"handleFileChange\"\r\n        :file-list=\"localFiles\"\r\n        accept=\".xlsx,.csv\"\r\n        :show-file-list=\"true\"\r\n      >\r\n        <el-button type=\"primary\" icon=\"el-icon-upload\" @click=\"showServerFiles = true\">选择本地文件（仅限.xlsx/.csv）</el-button>\r\n        <div slot=\"tip\" class=\"el-upload__tip\">每次最多上传5个文件，单个文件不超过100MB</div>\r\n      </el-upload>\r\n\r\n      <!-- 服务器文件选择 -->\r\n      <div v-if=\"showServerFiles\" class=\"server-files-section\">\r\n        <el-table\r\n          :data=\"serverFiles\"\r\n          border\r\n          fit\r\n          highlight-current-row\r\n          @selection-change=\"handleSelectionChange\"\r\n          v-loading=\"loading\"\r\n          style=\"margin:20px 0;\"\r\n          height=\"200\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column prop=\"fileName\" label=\"服务器文件\" min-width=\"250\">\r\n            <template #default=\"{row}\">\r\n              <i class=\"el-icon-folder-opened\" />\r\n              <span style=\"margin-left:8px\">{{ row.fileName }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n          <el-table-column prop=\"fileSize\" label=\"文件大小\" width=\"120\" align=\"center\">\r\n            <template #default=\"{row}\">\r\n              {{ (row.fileSize / 1024 / 1024).toFixed(2) }} MB\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div v-if=\"showServerFiles\" class=\"action-buttons\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"loadServerFiles\"\r\n          :loading=\"loading\"\r\n        >\r\n          刷新文件列表\r\n        </el-button>\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-s-data\"\r\n          :loading=\"processing\"\r\n          :disabled=\"selectedFiles.length === 0\"\r\n          @click=\"submitAnalysis\"\r\n        >\r\n          {{ processing ? '分析中...' : '开始分析' }}\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 异常类型选择 -->\r\n      <div v-if=\"Object.keys(anomalyData).length > 0\" class=\"anomaly-type-selector\">\r\n        <el-divider content-position=\"left\">异常数据类型</el-divider>\r\n        <div class=\"scrollable-radio-group\">\r\n          <el-radio-group v-model=\"currentAnomalyType\" @change=\"handleAnomalyTypeChange\">\r\n            <el-radio-button v-for=\"(value, key) in anomalyData\" :key=\"key\" :label=\"key\">\r\n              {{ anomalyTypeLabels[key] || key }} ({{ value.length }})\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 异常数据展示 -->\r\n      <el-divider content-position=\"left\">异常数据结果</el-divider>\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          :data=\"exceptionData\"\r\n          border\r\n          fit\r\n          highlight-current-row\r\n          v-loading=\"processing\"\r\n          style=\"width:100%;\"\r\n          height=\"500\"\r\n        >\r\n          <el-table-column prop=\"订单号\" label=\"订单号\" min-width=\"140\" align=\"center\" />\r\n          <el-table-column prop=\"支付人姓名\" label=\"支付人姓名\" min-width=\"100\" />\r\n          <el-table-column prop=\"支付人身份证号\" label=\"身份证号\" min-width=\"150\" />\r\n          <el-table-column prop=\"物流单号\" label=\"物流单号\" min-width=\"140\" />\r\n          <!-- <el-table-column prop=\"收货地址\" label=\"收货地址\" min-width=\"180\" show-overflow-tooltip />\r\n          <el-table-column prop=\"支付方式\" label=\"支付方式\" min-width=\"90\" />\r\n          <el-table-column prop=\"订单金额\" label=\"订单金额\" min-width=\"100\" align=\"right\" />\r\n          <el-table-column prop=\"订单状态\" label=\"订单状态\" min-width=\"90\" />\r\n          <el-table-column prop=\"创建时间\" label=\"创建时间\" min-width=\"140\" /> -->\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      localFiles: [],\r\n      serverFiles: [],\r\n      selectedFiles: [],\r\n      loading: false,\r\n      processing: false,\r\n      anomalyData: {},\r\n      exceptionData: [],\r\n      currentAnomalyType: '',\r\n      showServerFiles: false,\r\n      anomalyTypeLabels: {\r\n        '同一姓名多个身份证': '同一姓名多个身份证',\r\n        '同一身份证多个姓名': '同一身份证多个姓名',\r\n        '物流单号重复': '物流单号重复',\r\n        '订单号多个身份证': '订单号多个身份证'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始不加载服务器文件，等用户点击按钮后再加载\r\n  },\r\n  methods: {\r\n    async loadServerFiles() {\r\n      this.loading = true\r\n      try {\r\n        // 使用axios直接调用后端接口，避免可能的$http配置问题\r\n        const response = await axios.get('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        const data = response.data\r\n        // 根据后端返回的数据格式进行处理\r\n        if (data.paths && Array.isArray(data.paths)) {\r\n          this.serverFiles = data.paths.map(path => {\r\n            const fileName = path.split('\\\\').pop()\r\n            return {\r\n              fileName: fileName,\r\n              path: path,\r\n              uploadTime: new Date().toLocaleString(), // 如果后端没有提供时间，使用当前时间\r\n              fileSize: 1024 * 1024 // 默认大小，如果后端没有提供\r\n            }\r\n          })\r\n          console.log('成功获取服务器文件列表:', this.serverFiles)\r\n        } else {\r\n          console.error('服务器返回的文件列表格式不正确:', data)\r\n          this.$message.warning('服务器返回的文件列表格式不正确')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取服务器文件失败:', error)\r\n        this.$message.error(`获取服务器文件失败: ${error.message || '未知错误'}`)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleFileChange(file, fileList) {\r\n      if (fileList.length > 5) {\r\n        this.$message.warning('最多选择5个文件')\r\n        this.localFiles = fileList.slice(0, 5)\r\n        return false\r\n      }\r\n      this.localFiles = fileList\r\n      // 如果是第一次选择文件，自动加载服务器文件列表\r\n      if (this.showServerFiles === false && fileList.length > 0) {\r\n        this.showServerFiles = true\r\n        this.loadServerFiles()\r\n      }\r\n      return false\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection.map(item => item.path || item.fileName)\r\n      console.log('已选择文件:', this.selectedFiles)\r\n    },\r\n\r\n    handleAnomalyTypeChange(type) {\r\n      this.exceptionData = this.anomalyData[type] || []\r\n    },\r\n\r\n    async submitAnalysis() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请至少选择一个文件进行分析')\r\n        return\r\n      }\r\n      this.processing = true\r\n      try {\r\n        // 使用axios直接调用后端接口\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: this.selectedFiles\r\n        })\r\n        const data = response.data\r\n        console.log('分析结果:', data)\r\n        // 清空之前的数据\r\n        this.anomalyData = {}\r\n        this.exceptionData = []\r\n        this.currentAnomalyType = ''\r\n        // 处理返回的异常数据\r\n        if (data && typeof data === 'object') {\r\n          this.anomalyData = data\r\n          // 计算总异常记录数\r\n          const totalAnomalies = Object.values(data).reduce((sum, arr) => sum + arr.length, 0)\r\n          if (totalAnomalies > 0) {\r\n            this.$message.success(`发现 ${totalAnomalies} 条异常记录`)\r\n            // 默认显示第一个有数据的异常类型\r\n            for (const key in data) {\r\n              if (data[key].length > 0) {\r\n                this.currentAnomalyType = key\r\n                this.exceptionData = data[key]\r\n                break\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.info('未发现异常记录')\r\n          }\r\n        } else {\r\n          this.$message.info('未发现异常记录')\r\n        }\r\n      } catch (error) {\r\n        console.error('数据分析失败:', error)\r\n        this.$message.error(`数据分析失败: ${error.message || '未知错误'}`)\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.box-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);\r\n}\r\n\r\n.upload-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.server-files-section {\r\n  margin: 20px 0;\r\n}\r\n\r\n.action-buttons {\r\n  margin: 20px 0;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  gap: 10px;\r\n}\r\n\r\n.anomaly-type-selector {\r\n  margin: 20px 0;\r\n}\r\n\r\n.scrollable-radio-group {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  /* 强制显示滚动条，即使内容不够 */\r\n  overflow-y: scroll;\r\n}\r\n\r\n.el-divider {\r\n  margin: 24px 0;\r\n}\r\n\r\n.el-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.el-table::before {\r\n  height: 0;\r\n}\r\n\r\n::v-deep .el-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-table__row:hover {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 减小表格行高 */\r\n::v-deep .el-table__row {\r\n  height: 40px; /* 默认可能是 48px 左右 */\r\n}\r\n\r\n/* 减小表头高度 */\r\n::v-deep .el-table__header th {\r\n  padding: 8px 0; /* 默认可能是 12px 0 */\r\n}\r\n\r\n/* 减小单元格内边距 */\r\n::v-deep .el-table__cell {\r\n  padding: 8px 0; /* 默认可能是 12px 0 */\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.scrollable-radio-group::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.scrollable-radio-group::-webkit-scrollbar-thumb {\r\n  background-color: #909399;\r\n  border-radius: 3px;\r\n}\r\n\r\n.scrollable-radio-group::-webkit-scrollbar-track {\r\n  background-color: #F2F6FC;\r\n}\r\n\r\n.table-container {\r\n  width: 100%;\r\n  height: 500px; /* 固定高度，超出时显示滚动条 */\r\n  overflow-y: auto; /* 启用垂直滚动 */\r\n  overflow-x: hidden; /* 隐藏水平滚动条 */\r\n  margin-top: 20px;\r\n}/* 自定义表格滚动条样式 */\r\n.table-container::-webkit-scrollbar {\r\n  width: 8px;  /* 垂直滚动条宽度 */\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background-color: #909399;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background-color: #F2F6FC;\r\n}\r\n</style>\r\n"]}]}