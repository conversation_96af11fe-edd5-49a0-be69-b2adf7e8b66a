
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

def plot_pca(df_features, scores, out_path="pca_anomaly_plot.png"):
    scaler = StandardScaler()
    X = scaler.fit_transform(df_features)
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X)
    plt.figure(figsize=(8, 6))
    plt.scatter(X_pca[:, 0], X_pca[:, 1], c=scores["fused_score"], cmap="coolwarm", edgecolor='k', alpha=0.8)
    plt.colorbar(label="Fused Anomaly Score")
    plt.xlabel("PCA Component 1")
    plt.ylabel("PCA Component 2")
    plt.title("Account Anomaly Visualization (PCA)")
    plt.tight_layout()
    plt.savefig(out_path)
    plt.close()
