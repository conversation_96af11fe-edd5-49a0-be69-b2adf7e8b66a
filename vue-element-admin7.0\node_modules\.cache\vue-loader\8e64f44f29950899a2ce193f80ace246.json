{"remainingRequest": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue?vue&type=style&index=0&id=09ac478a&scoped=true&lang=css", "dependencies": [{"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\src\\components\\Charts\\OrderException.vue", "mtime": 1749200805698}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1731739006000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1731739008000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1731739008000}, {"path": "D:\\地下田庄2\\2025大创_地下田庄\\vue-element-admin7.0\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1731739002000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OrderException.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "OrderException.vue", "sourceRoot": "src/components/Charts", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span>异常物流订单分析系统</span>\r\n      </div>\r\n\r\n      <!-- 文件上传区域 -->\r\n      <el-upload\r\n        class=\"upload-container\"\r\n        action=\"#\"\r\n        :auto-upload=\"false\"\r\n        :limit=\"5\"\r\n        :on-change=\"handleFileChange\"\r\n        :file-list=\"localFiles\"\r\n        accept=\".xlsx,.csv\"\r\n        :show-file-list=\"true\"\r\n      >\r\n        <el-button type=\"primary\" icon=\"el-icon-upload\" @click=\"showServerFiles = true\">选择本地文件（仅限.xlsx/.csv）</el-button>\r\n        <div slot=\"tip\" class=\"el-upload__tip\">每次最多上传5个文件，单个文件不超过100MB</div>\r\n      </el-upload>\r\n\r\n      <!-- 服务器文件选择 -->\r\n      <div v-if=\"showServerFiles\" class=\"server-files-section\">\r\n        <el-table\r\n          :data=\"serverFiles\"\r\n          border\r\n          fit\r\n          highlight-current-row\r\n          @selection-change=\"handleSelectionChange\"\r\n          v-loading=\"loading\"\r\n          style=\"margin:20px 0;\"\r\n          height=\"200\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column prop=\"fileName\" label=\"服务器文件\" min-width=\"250\">\r\n            <template #default=\"{row}\">\r\n              <i class=\"el-icon-folder-opened\" />\r\n              <span style=\"margin-left:8px\">{{ row.fileName }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"uploadTime\" label=\"上传时间\" width=\"180\" align=\"center\" />\r\n          <el-table-column prop=\"fileSize\" label=\"文件大小\" width=\"120\" align=\"center\">\r\n            <template #default=\"{row}\">\r\n              {{ (row.fileSize / 1024 / 1024).toFixed(2) }} MB\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 操作按钮 -->\r\n      <div v-if=\"showServerFiles\" class=\"action-buttons\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-refresh\"\r\n          @click=\"loadServerFiles\"\r\n          :loading=\"loading\"\r\n        >\r\n          刷新文件列表\r\n        </el-button>\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-s-data\"\r\n          :loading=\"processing\"\r\n          :disabled=\"selectedFiles.length === 0\"\r\n          @click=\"submitAnalysis\"\r\n        >\r\n          {{ processing ? '分析中...' : '开始分析' }}\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 异常类型选择 -->\r\n      <div v-if=\"Object.keys(anomalyData).length > 0\" class=\"anomaly-type-selector\">\r\n        <el-divider content-position=\"left\">异常数据类型</el-divider>\r\n        <div class=\"scrollable-radio-group\">\r\n          <el-radio-group v-model=\"currentAnomalyType\" @change=\"handleAnomalyTypeChange\">\r\n            <el-radio-button v-for=\"(value, key) in anomalyData\" :key=\"key\" :label=\"key\">\r\n              {{ anomalyTypeLabels[key] || key }} ({{ value.length }})\r\n            </el-radio-button>\r\n          </el-radio-group>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 异常数据展示 -->\r\n      <el-divider content-position=\"left\">异常数据结果</el-divider>\r\n      <div class=\"table-container\">\r\n        <el-table\r\n          :data=\"exceptionData\"\r\n          border\r\n          fit\r\n          highlight-current-row\r\n          v-loading=\"processing\"\r\n          style=\"width:100%;\"\r\n          height=\"500\"\r\n        >\r\n          <el-table-column prop=\"订单号\" label=\"订单号\" width=\"180\" align=\"center\" />\r\n          <el-table-column prop=\"支付人姓名\" label=\"支付人姓名\" width=\"120\" />\r\n          <el-table-column prop=\"支付人身份证号\" label=\"身份证号\" width=\"180\" />\r\n          <el-table-column prop=\"物流单号\" label=\"物流单号\" width=\"180\" />\r\n          <!-- 添加更多列以确保内容宽度超出表格宽度，触发水平滚动条 -->\r\n          <el-table-column prop=\"收货地址\" label=\"收货地址\" width=\"250\" />\r\n          <el-table-column prop=\"支付方式\" label=\"支付方式\" width=\"120\" />\r\n          <el-table-column prop=\"订单金额\" label=\"订单金额\" width=\"120\" align=\"right\" />\r\n          <el-table-column prop=\"订单状态\" label=\"订单状态\" width=\"120\" />\r\n          <el-table-column prop=\"创建时间\" label=\"创建时间\" width=\"180\" />\r\n        </el-table>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'OrderException',\r\n  data() {\r\n    return {\r\n      localFiles: [],\r\n      serverFiles: [],\r\n      selectedFiles: [],\r\n      loading: false,\r\n      processing: false,\r\n      anomalyData: {},\r\n      exceptionData: [],\r\n      currentAnomalyType: '',\r\n      showServerFiles: false,\r\n      anomalyTypeLabels: {\r\n        '同一姓名多个身份证': '同一姓名多个身份证',\r\n        '同一身份证多个姓名': '同一身份证多个姓名',\r\n        '物流单号重复': '物流单号重复',\r\n        '订单号多个身份证': '订单号多个身份证'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始不加载服务器文件，等用户点击按钮后再加载\r\n  },\r\n  methods: {\r\n    async loadServerFiles() {\r\n      this.loading = true\r\n      try {\r\n        // 使用axios直接调用后端接口，避免可能的$http配置问题\r\n        const response = await axios.get('http://127.0.0.1:8000/get_all_TrackingNum')\r\n        const data = response.data\r\n        // 根据后端返回的数据格式进行处理\r\n        if (data.paths && Array.isArray(data.paths)) {\r\n          this.serverFiles = data.paths.map(path => {\r\n            const fileName = path.split('\\\\').pop()\r\n            return {\r\n              fileName: fileName,\r\n              path: path,\r\n              uploadTime: new Date().toLocaleString(), // 如果后端没有提供时间，使用当前时间\r\n              fileSize: 1024 * 1024 // 默认大小，如果后端没有提供\r\n            }\r\n          })\r\n          console.log('成功获取服务器文件列表:', this.serverFiles)\r\n        } else {\r\n          console.error('服务器返回的文件列表格式不正确:', data)\r\n          this.$message.warning('服务器返回的文件列表格式不正确')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取服务器文件失败:', error)\r\n        this.$message.error(`获取服务器文件失败: ${error.message || '未知错误'}`)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    handleFileChange(file, fileList) {\r\n      if (fileList.length > 5) {\r\n        this.$message.warning('最多选择5个文件')\r\n        this.localFiles = fileList.slice(0, 5)\r\n        return false\r\n      }\r\n      this.localFiles = fileList\r\n      // 如果是第一次选择文件，自动加载服务器文件列表\r\n      if (this.showServerFiles === false && fileList.length > 0) {\r\n        this.showServerFiles = true\r\n        this.loadServerFiles()\r\n      }\r\n      return false\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.selectedFiles = selection.map(item => item.path || item.fileName)\r\n      console.log('已选择文件:', this.selectedFiles)\r\n    },\r\n\r\n    handleAnomalyTypeChange(type) {\r\n      this.exceptionData = this.anomalyData[type] || []\r\n    },\r\n\r\n    async submitAnalysis() {\r\n      if (this.selectedFiles.length === 0) {\r\n        this.$message.warning('请至少选择一个文件进行分析')\r\n        return\r\n      }\r\n      this.processing = true\r\n      try {\r\n        // 使用axios直接调用后端接口\r\n        const response = await axios.post('http://127.0.0.1:8000/get_sus_TrackingNum', {\r\n          filenames: this.selectedFiles\r\n        })\r\n        const data = response.data\r\n        console.log('分析结果:', data)\r\n        // 清空之前的数据\r\n        this.anomalyData = {}\r\n        this.exceptionData = []\r\n        this.currentAnomalyType = ''\r\n        // 处理返回的异常数据\r\n        if (data && typeof data === 'object') {\r\n          this.anomalyData = data\r\n          // 计算总异常记录数\r\n          const totalAnomalies = Object.values(data).reduce((sum, arr) => sum + arr.length, 0)\r\n          if (totalAnomalies > 0) {\r\n            this.$message.success(`发现 ${totalAnomalies} 条异常记录`)\r\n            // 默认显示第一个有数据的异常类型\r\n            for (const key in data) {\r\n              if (data[key].length > 0) {\r\n                this.currentAnomalyType = key\r\n                this.exceptionData = data[key]\r\n                break\r\n              }\r\n            }\r\n          } else {\r\n            this.$message.info('未发现异常记录')\r\n          }\r\n        } else {\r\n          this.$message.info('未发现异常记录')\r\n        }\r\n      } catch (error) {\r\n        console.error('数据分析失败:', error)\r\n        this.$message.error(`数据分析失败: ${error.message || '未知错误'}`)\r\n      } finally {\r\n        this.processing = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.box-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);\r\n}\r\n\r\n.upload-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.server-files-section {\r\n  margin: 20px 0;\r\n}\r\n\r\n.action-buttons {\r\n  margin: 20px 0;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  gap: 10px;\r\n}\r\n\r\n.anomaly-type-selector {\r\n  margin: 20px 0;\r\n}\r\n\r\n.scrollable-radio-group {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n  border: 1px solid #EBEEF5;\r\n  border-radius: 4px;\r\n  /* 强制显示滚动条，即使内容不够 */\r\n  overflow-y: scroll;\r\n}\r\n\r\n.el-divider {\r\n  margin: 24px 0;\r\n}\r\n\r\n.el-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.el-table::before {\r\n  height: 0;\r\n}\r\n\r\n::v-deep .el-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-table__row:hover {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 减小表格行高 */\r\n::v-deep .el-table__row {\r\n  height: 40px; /* 默认可能是 48px 左右 */\r\n}\r\n\r\n/* 减小表头高度 */\r\n::v-deep .el-table__header th {\r\n  padding: 8px 0; /* 默认可能是 12px 0 */\r\n}\r\n\r\n/* 减小单元格内边距 */\r\n::v-deep .el-table__cell {\r\n  padding: 8px 0; /* 默认可能是 12px 0 */\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.scrollable-radio-group::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.scrollable-radio-group::-webkit-scrollbar-thumb {\r\n  background-color: #909399;\r\n  border-radius: 3px;\r\n}\r\n\r\n.scrollable-radio-group::-webkit-scrollbar-track {\r\n  background-color: #F2F6FC;\r\n}\r\n\r\n.table-container {\r\n  width: 100%;\r\n  height: 500px; /* 固定高度，超出时显示滚动条 */\r\n  overflow-y: auto; /* 启用垂直滚动 */\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 自定义表格滚动条样式 */\r\n.table-container::-webkit-scrollbar {\r\n  width: 8px;  /* 垂直滚动条宽度 */\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background-color: #909399;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background-color: #F2F6FC;\r\n}\r\n</style>\r\n"]}]}