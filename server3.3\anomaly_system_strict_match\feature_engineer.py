
import pandas as pd
import numpy as np
from collections import Counter
from scipy.stats import entropy

def extract_features(df):
    grp = df.groupby("交易户名", group_keys=False)
    features = []
    for name, group in grp:
        group = group.sort_values("交易时间")
        times = group["交易时间"].values.astype("datetime64[s]")
        if len(times) >= 2:
            interval = np.diff(times).astype(float)
            median_interval = np.median(interval)
        else:
            median_interval = 0
        try:
            t_in = group[group["txn_amount"] > 0]["交易时间"].iloc[0]
            t_out = group[group["txn_amount"] < 0]["交易时间"].iloc[-1]
            time_to_clear = (t_out - t_in).total_seconds()
        except:
            time_to_clear = 0
        small_seq = (group["is_small"].astype(int).rolling(window=3).sum() >= 3).sum()
        alt_count = sum(group["direction"].ne(group["direction"].shift()))
        hour_freqs = np.array(list(Counter(group["hour"]).values())) / len(group)
        h_entropy = entropy(hour_freqs, base=2) if len(hour_freqs) > 1 else 0
        in_sum = group[group["txn_amount"] > 0]["txn_amount"].sum()
        out_sum = -group[group["txn_amount"] < 0]["txn_amount"].sum()
        loop_ratio = in_sum / out_sum if out_sum > 0 else 0
        max_amt_ratio = abs(group["txn_amount"]).max() / abs(group["txn_amount"]).sum() if abs(group["txn_amount"]).sum() > 0 else 0
        with_flag = group["标记"].notna().any()
        row = {
            "txn_count": len(group),
            "unique_days": group["date"].nunique(),
            "mean_daily_txn": len(group) / max(1, group["date"].nunique()),
            "burst_txn_max": group["date"].value_counts().max(),
            "median_txn_interval": median_interval,
            "time_to_clear": time_to_clear,
            "net_inflow_rate": in_sum / out_sum if out_sum > 0 else 0,
            "loopback_ratio": loop_ratio,
            "max_single_txn_ratio": max_amt_ratio,
            "hourly_entropy": h_entropy,
            "is_partner_with_flagged": int(with_flag),
            "median_balance": group["交易余额"].median(),
            "low_balance_periods": (group["交易余额"] < group["交易余额"].mean() * 0.2).mean(),
            "suspicious_bursts": int(small_seq),
            "in_out_alternations": alt_count,
            "small_txn_ratio": group["is_small"].mean(),
        }
        features.append((name, row))
    return pd.DataFrame.from_dict(dict(features), orient="index").fillna(0)
