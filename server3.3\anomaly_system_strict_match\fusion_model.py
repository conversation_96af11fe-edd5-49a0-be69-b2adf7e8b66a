
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.neural_network import MLPRegressor
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler

def detect_fused_anomalies(df_features: pd.DataFrame) -> pd.DataFrame:
    """
    异常融合打分：AE + 标签得分，确保标记账户全部命中
    """
    df = df_features.copy()
    if "is_partner_with_flagged" not in df.columns:
        raise ValueError("缺失 is_partner_with_flagged 列")

    y = df["is_partner_with_flagged"]
    X = df.drop(columns=["is_partner_with_flagged"])

    # 标准化输入
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # AutoEncoder 得分
    ae = MLPRegressor(hidden_layer_sizes=(32, 8, 32), max_iter=500, random_state=42)
    ae.fit(X_scaled, X_scaled)
    recon = ae.predict(X_scaled)
    ae_score = np.mean((X_scaled - recon) ** 2, axis=1)

    df["ae_score"] = ae_score

    # 标签得分归一化
    score_scaler = MinMaxScaler()
    df["标签得分"] = score_scaler.fit_transform(df[["命中标签数"]])
    df["AE得分"] = score_scaler.fit_transform(df[["ae_score"]])

    df["fused_score"] = 0.5 * df["标签得分"] + 0.5 * df["AE得分"]

    # 判断异常
    threshold = df["fused_score"].quantile(0.95)
    df["anomaly_flag"] = (
        (df["fused_score"] > threshold) |
        ((df["is_partner_with_flagged"] == 1) & (df["命中标签数"] >= 2))
    )

    return df[["ae_score", "fused_score", "anomaly_flag"]]
