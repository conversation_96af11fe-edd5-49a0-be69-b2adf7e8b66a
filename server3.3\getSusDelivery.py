import pandas as pd

import pandas as pd
from collections import defaultdict

def check_express_data(csv_path):
    # 读取 CSV 文件
    df = pd.read_csv(csv_path)

    # 清洗数据：去除空格和缺失值
    df = df.dropna(subset=['tracking_number', 'name', 'id_number'])
    df['tracking_number'] = df['tracking_number'].astype(str).str.strip()
    df['name'] = df['name'].astype(str).str.strip()
    df['id_number'] = df['id_number'].astype(str).str.strip()

    # 一、检查重复快递单号
    duplicate_tracking = df[df.duplicated('tracking_number', keep=False)]

    # 二、一个身份证号对应多个名字
    id_to_names = df.groupby('id_number')['name'].nunique()
    multiple_names_per_id = id_to_names[id_to_names > 1]

    # 三、一个名字对应多个身份证号
    name_to_ids = df.groupby('name')['id_number'].nunique()
    multiple_ids_per_name = name_to_ids[name_to_ids > 1]

    # 返回结果
    return {
        'duplicate_tracking': duplicate_tracking,
        'multiple_names_per_id': df[df['id_number'].isin(multiple_names_per_id.index)],
        'multiple_ids_per_name': df[df['name'].isin(multiple_ids_per_name.index)],
    }
