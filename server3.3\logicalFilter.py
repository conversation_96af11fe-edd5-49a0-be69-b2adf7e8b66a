import pymysql
from getForms import get_forms
from isNumber import is_number
def logical_filter(org_form, logic: list, new_form,connect_info):
    """
    运用正则关系，缩小查询范围，并把结果存到一个表中！！！这里最好要实现且逻辑的优先级是要高于或逻辑的！！！
    不过直接把逻辑筛选传到sql的where里，就可以自动实现且逻辑的优先级大于或逻辑
    :param org_form: 原始的数据表名
    :param logic: 存储各种筛选逻辑，包含attribute（属性），lgc（大于、小于、非这种逻辑关系），content（输入的内容），connect（且或这种连接关系）。
    :param new_form: 新创建的数据表名
    :return: 0（正常），1（没有逻辑筛选，请使用原表），2（输入的属性，逻辑关系，输入内容或连接关系不匹配），3（新表已存在，请删除原新表名后重试），
    4（原表名不存在）
    """
    if len(logic) == 0:
        return 1
    backup_connection = {
        "host":"***********",
        "user":"root",
        "password":"123456",
        "database":"digit_check"
    }
    connect_dic = connect_info if connect_info else backup_connection
    connection = pymysql.connect(**connect_dic)
    cursor = connection.cursor()
    select_condition_str = ""
    if new_form in get_forms(connect_dic):
        cursor.close()
        return 3
    if org_form not in get_forms(connect_dic):
        cursor.close()
        return 4
    for i in range(len(logic)):
        if i != 0:
            if logic[i]['connect'] == "且":
                select_condition_str += " and "
            elif logic[i]['connect'] == "或":
                select_condition_str += " or "
        select_condition_str += logic[i]['attribute']
        if logic[i]['logical'] == "大于":
            select_condition_str += " > "
        elif logic[i]['logical'] == "大于等于":
            select_condition_str += " >= "
        elif logic[i]['logical'] == "小于":
            select_condition_str += " < "
        elif logic[i]['logical'] == "小于等于": # 这之前传的版本写错了
            select_condition_str += " <= "
        elif logic[i]['logical'] == "等于":
            select_condition_str += " = "
        elif logic[i]['logical'] == "不等于":
            select_condition_str += " != "
        elif logic[i]['logical'] == "是":
            select_condition_str += " is "
        elif logic[i]['logical'] == "非":
            select_condition_str += " is not "
        if logic[i]['content'] == '空' or logic[i]['content'] == 'NULL' or logic[i]['content'] == 'null':
            select_condition_str += "NULL"
        elif is_number(logic[i]['content']):
            select_condition_str += logic[i]['content']
        else:
            select_condition_str = select_condition_str + "'" + logic[i]['content'] + "'"

    select_query = f"""
        select * 
        from {org_form}
        where {select_condition_str}
    """

    cursor.execute(select_query)
    query_results = cursor.fetchall()

    create_table_query = f"""
        CREATE TABLE IF NOT EXISTS {new_form} (
            `交易户名` VARCHAR(64) NOT NULL,
            `交易卡号` VARCHAR(64) NOT NULL,
            `交易账号` varchar(64),
            `交易时间` DATETIME,
            `收付标志` VARCHAR(64),
            `交易金额` DECIMAL(18, 2),
            `交易余额` DECIMAL(18,2),
            `交易币种` varchar(64),
            `对手账号` VARCHAR(64),
            `对手户名` VARCHAR(64),
            `交易网点名称` VARCHAR(64),
            `对手开户银行` varchar(64),
            `备注` varchar(512),
            `摘要说明` varchar(512),
            `IP地址` varchar(512),
            `MAC地址` varchar(512)
        );
        """
    cursor.execute(create_table_query)
    connection.commit()

    insert_query = f"""
        INSERT INTO {new_form} (
            交易户名, 交易卡号, 交易账号, 交易时间, 收付标志, 交易金额, 交易余额, 交易币种, 
            对手账号, 对手户名, 交易网点名称, 对手开户银行, 备注, 摘要说明, `IP地址`, `MAC地址`
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
    cursor.executemany(insert_query, query_results)
    connection.commit()
    cursor.close()
    return 0