import pandas as pd
import numpy as np
from dateutil import parser

# 尝试解析时间字符串
def _try_parse(text):
    try:
        return parser.parse(str(text), fuzzy=True)
    except Exception:
        return pd.NaT

# 对 Series 进行稳健的日期解析
def _robust_datetime(series):
    parsed = pd.to_datetime(series, errors="coerce")
    mask = parsed.isna()
    if mask.any():
        parsed.loc[mask] = series.loc[mask].apply(_try_parse)
    return pd.to_datetime(parsed, errors="coerce")

# 读取 Excel 文件并处理时间、特征等
def load_excel(file_path, drop_bad_time=False):
    df = pd.read_excel(file_path, dtype={"交易户名": str})

    # 解析交易时间列
    df["交易时间"] = _robust_datetime(df["交易时间"])
    bad = df["交易时间"].isna()
    if bad.any():
        if drop_bad_time:
            df = df[~bad]
        else:
            raise ValueError("交易时间无法解析")

    # 构造新特征
    df["txn_amount"] = df["转入金额"].fillna(0) - df["转出金额"].fillna(0)
    df["hour"] = df["交易时间"].dt.hour
    df["date"] = df["交易时间"].dt.date
    df["direction"] = np.where(df["txn_amount"] >= 0, "in", "out")
    df["is_small"] = df["txn_amount"].abs() < df["txn_amount"].abs().quantile(0.2)
    df["is_low_balance"] = (df["交易余额"] > 0) & (
        df["交易余额"] < df[df["交易余额"] > 0]["交易余额"].quantile(0.25)
    )

    return df
